{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from joblib import load\n", "import json\n", "import numpy as np\n", "import os\n", "import plotly.express as px\n", "import polars as pl\n", "pl.Config.set_tbl_rows(30)\n", "import polars.selectors as cs\n", "import pprint\n", "from record_linkage import RecordLinkage\n", "from sklearn.metrics import confusion_matrix, classification_report, f1_score"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# set if you want to export the comps data frame for model training\n", "EXPORT_COMPS_FOR_TRAINING = False\n", "# model that the export will be trained for\n", "EXPORT_MODEL_NAME = 'name_race_gender_exact_name'\n", "# ml data folder\n", "EXPORT_MODEL_DATA = 'ml/data'\n", "os.makedirs(EXPORT_MODEL_DATA,exist_ok=True)\n", "# filename to use for training export\n", "EXPORT_COMPS_FOR_TRAINING_FILENAME = f\"{EXPORT_MODEL_DATA}/{EXPORT_MODEL_NAME}_{str(datetime.now().strftime('%Y%m%d'))}.csv\"\n", "# flag if there is a label with this dataset that can be used for training an assessing accuracy\n", "HAS_LABEL = True\n", "# name of the label field\n", "LABEL_FLD = 'true_match'\n", "# models folder\n", "MODELS_FOLDER = 'ml/models'\n", "# dedup flag\n", "DEDUP = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Preprocessing](https://recordlinkage.readthedocs.io/en/latest/ref-preprocessing.html)\n", "\n", "Setup the data sources dictionary for every data source that will be used in the record linkage, specify the data source, DataFrame variable, the name fields that will be preprocessed and the name for the unique key, dataset_key and alternate_key.  The alternate_key is setup if there is another key that can be used as a reference, but the dataset_key is what will be used to generate the unique key. The cleaning will be done on the **name_fields** for each datasource where it is run through the Record Linkage Toolkit's cleaning methods and it wil also get the phonetic Soundex code from the cleaned version of the name."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1_file_path = 'data/main_fake_dataset_50K_with_twins.csv'\n", "ds2_file_path = 'data/sample_fake_dataset_50K_with_twins.csv'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["name_fields = ['first_name','middle_name','last_name']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl = RecordLinkage(dedup=DEDUP)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1 = pl.read_csv(ds1_file_path,schema_overrides={'birth_date':pl.Date})\n", "ds1_dataset_key = 'index'\n", "\n", "ds1 = ds1.with_columns(pl.col('name_last').str.replace('-',' ').str.strip_chars().str.split(by=' ').alias('name_last_list'))\n", "ds1 = RecordLinkage.expand_multiple_values_to_new_rec(ds1,'name_last','name_last_list')\n", "\n", "\n", "ds1 = RecordLinkage.assign_unique_key(df=ds1,\n", "                                key_fld=ds1_dataset_key,\n", "                                unique_key_name=rl.unique_key_name)\n", "ds1 = RecordLinkage.rename_columns(ds1,\n", "                             first_name='name_first',\n", "                             middle_name='name_middle',\n", "                             last_name='name_last',\n", "                             birth_date='birth_date',\n", "                             gender='gender',\n", "                             race='bifsg_race',\n", "                             latitude='latitude',\n", "                             longitude='longitude')\n", "\n", "ds1 = ds1.to_pandas()\n", "ds1['birth_date'] = ds1['birth_date'].astype('datetime64[ns]')\n", "\n", "ds1_ds = RecordLinkage.create_dataset_dict('ds1', ds1, ds1_dataset_key, rl.unique_key_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1_ds['ds1']['datasource']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds2 = pl.read_csv(ds2_file_path,schema_overrides={'birth_date':pl.Date})\n", "ds2_dataset_key = 'index'\n", "\n", "ds2 = ds2.with_columns(pl.col('name_last').str.replace('-',' ').str.strip_chars().str.split(by=' ').alias('name_last_list'))\n", "ds2 = RecordLinkage.expand_multiple_values_to_new_rec(ds2,'name_last','name_last_list')\n", "\n", "ds2 = RecordLinkage.assign_unique_key(df=ds2,\n", "                                key_fld=ds2_dataset_key,\n", "                                unique_key_name=rl.unique_key_name)\n", "ds2 = RecordLinkage.rename_columns(ds2,\n", "                             first_name='name_first',\n", "                             middle_name='name_middle',\n", "                             last_name='name_last',\n", "                             birth_date='birth_date',\n", "                             gender='gender',\n", "                             race='bifsg_race',\n", "                             latitude='latitude',\n", "                             longitude='longitude')\n", "ds2 = ds2.to_pandas()\n", "ds2['birth_date'] = ds2['birth_date'].astype('datetime64[ns]')\n", "ds2_ds = RecordLinkage.create_dataset_dict('ds2', ds2, ds2_dataset_key, rl.unique_key_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds2_ds['ds2']['datasource']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds2_ds['ds2']['datasource']['ds2_sample_modified_code'].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Record Linkage Combinatons\n", "\n", "Call **get_rl_combinations** passing in the data source dictionary (**ds**) and optionally the main dataset key (**main_ds**) that will control how the record linkage combinations will be applied.  If a main dataset key is provided that data source will be used in the record linkage with all the data sources in the ds keys.  \n", "* ds keys = [ds1,ds2,ds3]\n", "* main_ds = 'ds1'\n", "* combinations = [(ds1,ds2),(ds1,ds3)]\n", "\n", "If no main data set key is provided all combinations of the data source keys will be used in the record linkage. \n", "* ds keys = [ds1,ds2,ds3]\n", "* main_ds = None\n", "* combinations = [(ds1,ds2),(ds1,ds3),(ds2,ds3)]\n", "\n", "If the **main_ds** key is provided and the **dedup** parameter is True this will perform a deduplication process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds = {**ds1_ds, **ds2_ds}\n", "rl_dict = RecordLinkage.get_rl_combinations(ds=ds,main_ds=['ds1'],dedup=DEDUP)\n", "pprint.pprint(rl_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Indexing](https://recordlinkage.readthedocs.io/en/latest/ref-index.html)\n", "\n", "Use a sorted neighborhood index on a combination of the Soundex first name and birthdate and Soundex last name and birthdate using the window size to account for misspellings in the first or last name to get the neighboring records."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for rl_key in rl_dict.keys():\n", "    RecordLinkage.run_sorted_neighborhood_indexing(rl_dict[rl_key],ds,window_size=9)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if EXPORT_COMPS_FOR_TRAINING:\n", "    for rl_key in rl_dict.keys():\n", "        rl_dict[rl_key]['rl'].run_negative_sampling_process()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl_dict['ds1_ds2']['rl'].candidates"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Compare Fields](https://recordlinkage.readthedocs.io/en/latest/ref-compare.html)\n", "Define a list of dictionaries that will have the following keys:\n", "* **ds1_fld** *Required*: Name of the field in dataset 1.\n", "* **ds2_fld** *Required*: Name of the field in dataset 2.\n", "* **compare_type** *Required*: Comparison method to use (exact, string, date or geo).\n", "* **match_fldname** *Required*: Name for the field name in the comparison matched output.\n", "* **weight** *Required*: Value between in 0 and 1.  The sum of all the fields weight values must equal to 1.  Can be set to None if weighting will not be used.\n", "* **method**: String similarity method (only when ```compare_type == 'string'```).  Values can be jar<PERSON><PERSON><PERSON>, jaro, levenshtein, damerau_levenshtein, qgram or cosine. \n", "* **method**: Method for comparing geographic distance (only when ```compare_type == 'geo'```).  Values can be linear, step, exp, guass or squared.\n", "* **thresh**: Threshold of what is considered a match (only when ```compare_type == 'string'|'geo'```).  Values can range from 0 to 1.\n", "* **left_on_lat**: Latitude field for dataset 1.\n", "* **left_on_lng**: Longitude field for dataset 1.\n", "* **right_on_lat**: Latitude field for dataset 2.\n", "* **right_on_lng**: Longitude field for dataset 2."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Comparison Fields\n", "Set the different combination of fields that will be used in the comparison matching.  Currently there are the following comparison field combinations.\n", "*   **name_race_gender** - Weighted comparision using the first, middle and last names, the date of birth and the race and gender fields.\n", "*   **name_race_gender_coords** - Weighted comparision using the same fields as **name_race_gender** with the addition of the address coordinates.\n", "*   **name_race_gender_exact_name** - Weighted comparison using the same fields as **name_race_gender** with the only addition of performing an exact match on the name fields in addition to the string similarity methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('compare_methods.json','r') as cm:\n", "    compare_methods = json.load(cm)\n", "compare_methods.keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Assign <PERSON><PERSON><PERSON><PERSON> to Record Linkage Dictionary\n", "For each record linkage being performed assign the comparision name that will be used for the matching."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl_dict['ds1_ds2']['compare_name'] = \"name_race_gender_exact_name\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Records\n", "Run comparison matching for each record linkage by calling **compare_records** passing in the comparison field mapping."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for rl_key in rl_dict:\n", "    print(rl_key)\n", "    compare_name = rl_dict[rl_key]['compare_name']\n", "    compare_flds = compare_methods[compare_name]\n", "    \n", "    print(f'\\nRecord Linkage: {rl_key}')\n", "    match_flds = [x['match_fldname'] for x in compare_flds]\n", "    \n", "    rl_dict[rl_key]['compare_fields'] = (\n", "        RecordLinkage.set_comparison_fields(compare_flds,\n", "        rl_dict[rl_key]['rl_ds1'],\n", "        rl_dict[rl_key]['rl_ds2'])\n", "        )\n", "    \n", "    rl_dict[rl_key]['rl'].compare_records(rl_dict[rl_key]['compare_fields'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Data for Machine Learning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if EXPORT_COMPS_FOR_TRAINING:\n", "    ds1_unique_key = rl_dict[rl_key]['rl_ds1_unique_key']\n", "    ds1_dataset_key = rl_dict[rl_key]['rl_ds1_dataset_key']\n", "    ds2_unique_key = rl_dict[rl_key]['rl_ds2_unique_key']\n", "    ds2_dataset_key = rl_dict[rl_key]['rl_ds2_dataset_key']\n", "    comps_ml = rl_dict[rl_key]['rl'].comps.copy().reset_index()\n", "    ds1_no_index = ds['ds1']['datasource'][[ds1_dataset_key]].reset_index()\n", "    ds2_no_index = ds['ds2']['datasource'][[ds2_dataset_key]].reset_index()\n", "    comps_ml = (comps_ml\n", "     .merge(ds1_no_index,\n", "            how='left',\n", "            left_on=ds1_unique_key,\n", "            right_on=ds1_unique_key)\n", "     .merge(ds2_no_index,\n", "            how='left',\n", "            left_on=ds2_unique_key,\n", "            right_on=ds2_unique_key))\n", "    comps_ml['true_match'] = np.where(comps_ml[ds1_dataset_key] == comps_ml[ds2_dataset_key],1,0)\n", "    comps_ml.set_index([ds1_unique_key,ds2_unique_key],inplace=True)\n", "    comps_ml.drop([ds1_dataset_key,ds2_dataset_key],axis=1,inplace=True)\n", "    comps_ml.to_csv(EXPORT_COMPS_FOR_TRAINING_FILENAME)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Classification](https://recordlinkage.readthedocs.io/en/latest/ref-classifiers.html)\n", "Load the Random Forest classification models, this will use the comparisons stored in the **comps** data frame to make predictions and the predictions will be stored in the record_linkage object's **preds** variable. A **match_score** is calcuated which is either a weighted sum or the number of matched fields divided by the total fields based on how the weight variable was set in the comparison fields. The predictions are stored in the **match_pred** field. If this is a labeled datatset a field will be created based on what is passed in the **LABEL_FLD** variable and will indicated if the records are a match with what was predicted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Classification Models\n", "* Load the classification models that are saved in the **models** directory.\n", "* Based on the **compare_name** that was set set the **model** key to point to the correct model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load random forest classification models\n", "rf_name_dob_gender_race = load(f'{MODELS_FOLDER}/name_dob_gender_race.joblib')\n", "rf_comps_name_race_gender_exact_name = load(f'{MODELS_FOLDER}/name_race_gender_exact_name.joblib')\n", "rf_name_dob_gender_race_coord = load(f'{MODELS_FOLDER}/name_dob_gender_race_coord.joblib')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    if rl_dict[key]['compare_name'] == 'name_race_gender_coords':\n", "        rl_dict[key]['model'] = rf_name_dob_gender_race_coord\n", "    elif rl_dict[key]['compare_name'] == 'name_race_gender_exact_name':\n", "        rl_dict[key]['model'] = rf_comps_name_race_gender_exact_name\n", "    elif rl_dict[key]['compare_name'] == 'name_race_gender':\n", "        rl_dict[key]['model'] = rf_name_dob_gender_race"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Make Predictions\n", "* Call the **predict** method to make predictions using the assigned model for each record linkage combination.\n", "* Calculate the **match_score** variable.\n", "* If this is a labeled dataset\n", "  * Calculate the **LABEL_FLD** to indicate a true match.\n", "  * Calculate fields to track true positive (**tp**), false negative (**fn**), true negative (**tn**) and false positive (**fp**) metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    print(f'\\nRecord Linkage: {key}')\n", "    model = rl_dict[key]['model']\n", "    rl_dict[key]['rl'].predict(model)\n", "    rl_dict[key]['rl'].calc_match_score2()\n", "    comps = rl_dict[key]['rl'].comps\n", "    if HAS_LABEL:\n", "        ds_key1 = rl_dict[key]['rl'].ds1_dataset_key\n", "        ds_key2 = rl_dict[key]['rl'].ds2_dataset_key\n", "        rl_dict[key]['rl'].comps[LABEL_FLD] = np.where(rl_dict[key]['rl'].comps[ds_key1] == rl_dict[key]['rl'].comps[ds_key2],1,0)\n", "        rl_dict[key]['rl'].comps['tp'] = np.where(((comps[LABEL_FLD] == 1) & (comps['match_pred'] == 1)),1,0)\n", "        rl_dict[key]['rl'].comps['fn'] = np.where(((comps[LABEL_FLD] == 1) & (comps['match_pred'] == 0)),1,0)\n", "        rl_dict[key]['rl'].comps['tn'] = np.where(((comps[LABEL_FLD] == 0) & (comps['match_pred'] == 0)),1,0)\n", "        rl_dict[key]['rl'].comps['fp'] = np.where(((comps[LABEL_FLD] == 0) & (comps['match_pred'] == 1)),1,0)\n", "        rl_dict[key]['rl'].comps = comps\n", "    del comps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Evaluation](https://recordlinkage.readthedocs.io/en/latest/ref-evaluation.html)\n", "\n", "Check how the match scores and predictions vary.  This will typically show that higher match scores have more predictions as matches.  Since this is a labeled dataset we can also run the confusion matrix and examine the false positive and false negatives."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Predictions by Score\n", "* Get a summary of the count of each prediction class (0 or 1) for each match score.\n", "* Print which scores have counts for both classes\n", "* Visualize the count of records by match score using seperate color for each prediction class to show how predictions change as the match score changes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    print(f'\\nRecord Linkage: {key}')\n", "    print(rl_dict[key]['rl'].get_score_pred_summary())\n", "    \n", "    print(rl_dict[key]['rl'].scores_pred.query('match_score >= .5'))\n", "    print(f\"Match scores {rl_dict[key]['rl'].scores_review} have predictions for matches and non matches.\")\n", "    \n", "    \n", "    scores_pred = rl_dict[key]['rl'].scores_pred.explode(['predictions','number_rows'])\n", "    fig = px.line(scores_pred, \n", "                x=\"match_score\", \n", "                y=\"number_rows\", \n", "                color='predictions',\n", "                color_discrete_sequence=['red','green'],\n", "                title=f'Predictions by Score for {key}',\n", "                labels={\n", "                        \"match_score\": \"Match Score\",\n", "                        \"number_rows\": \"Number of Records\",\n", "                        \"predictions\": \"Prediction\"\n", "                    },\n", "                )\n", "    fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Confusion Matrix and F1-Score\n", "HAS_LABEL must be True and LABEL_FLD must be populated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if HAS_LABEL:\n", "    for key in rl_dict:\n", "        print(f'\\nRecord Linkage: {key}')\n", "        cm = confusion_matrix(rl_dict[key]['rl'].comps[LABEL_FLD],\n", "        rl_dict[key]['rl'].comps['match_pred'])\n", "        f1 = f1_score(rl_dict[key]['rl'].comps[LABEL_FLD],\n", "        rl_dict[key]['rl'].comps['match_pred'])\n", "        tn, fp, fn, tp = cm.ravel()\n", "        print(f\"\"\"\n", "Confusion Matrix:\n", "[TP:{tp} \\t FN:{fn}]\n", "[FP:{fp} \\t TN:{tn}]\n", "        \n", "F1-Score: {f1}\n", "\"\"\")\n", "        \n", "        print(classification_report(rl_dict[key]['rl'].comps[LABEL_FLD],rl_dict[key]['rl'].comps['match_pred']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scores by Field Prediction Combinations\n", "\n", "This will get all the combinations of fields that matched or didn't match when doing the field compariosions and have them organized by score and colored as red (0) and green (1)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sfpc_list = []\n", "SCORES_BY_FIELD_THRESHOLD = .6\n", "for key in rl_dict:\n", "    comps = rl_dict[key]['rl'].comps\n", "    # create list of match fields\n", "    # match_fields = [c for c in comps.columns if 'match_' in c if c not in ['match_birth_date']]\n", "    match_fields = [x['match_fldname'] for x in rl_dict[key]['compare_fields'] if x['match_fldname'] not in ['match_birth_date']] + ['match_pred','match_score']\n", "    # create new field for aggregation\n", "    comps['number_of_records'] = 1\n", "    # new data frame where the number of records are added up for all the match_field combinations\n", "    scores_by_field = (comps[match_fields+['number_of_records']]\n", "                   .groupby(match_fields)['number_of_records']\n", "                   .sum()\n", "                   .reset_index()\n", "                   .sort_values(['match_score','number_of_records'],ascending=[True, False]))\n", "    # subset of match fields that will be styled, birth_date not needed since all records match\n", "    style_subset = [f for f in match_fields if f not in ['match_birth_date','match_score']]+['number_of_records']\n", "    styler = scores_by_field.query(f\"match_score >= {SCORES_BY_FIELD_THRESHOLD}\").style.background_gradient(cmap='RdYlGn',subset=style_subset)\n", "    styler.format(precision = 2)\n", "    outpath=f\"output/scores_by_field/{key}\"\n", "    os.makedirs(outpath,exist_ok=True)\n", "    styler.to_html(f'{outpath}/{key}.html')\n", "    styler.to_excel(f'{outpath}/{key}.xlsx',index=None)\n", "    sfpc_list.append(styler)\n", "sfpc_list[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Explore False Predictions\n", "* If **HAS_LABEL** is True then explore how many false positives there are at a specified threshold.\n", "* In the dataset created for training there is a field called **sample_modified_code** that says how the record was modified to assess record linkage quality.  If there is no such field in the dataset being used ignore.\n", "* Plot the count of false positive (fp==1) records by match score"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if HAS_LABEL:\n", "    metric='fp'\n", "    threshold=.5\n", "    key=list(rl_dict.keys())[0]\n", "\n", "    print(f\"{key} - {metric}\")\n", "    comps = rl_dict[key]['rl'].comps\n", "    match_score_counts = comps.query(f\"{metric} == 1\")['match_score'].value_counts()\n", "    modified_code_counts = (\n", "        comps\n", "        .query(f\"match_score > {threshold} and {metric} == 1\")\n", "        .merge(ds['ds2']['datasource']\n", "                .reset_index()\n", "                [['ds2_unique_pk','ds2_sample_modified_code']])\n", "        [['match_score','ds2_sample_modified_code']]\n", "        .value_counts())\n", "    print(f\"\"\"\\nMatch Scores ({metric}): \n", "{match_score_counts}\"\"\")\n", "\n", "    print(f\"\"\"\\nSample Modified Codes ({metric}):\n", "{modified_code_counts}\"\"\") \n", "\n", "    rl_dict[key]['rl'].comps[[metric,'match_score']].groupby('match_score').agg({metric:'sum'}).plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Matches & Check for Duplicates\n", "* Call **set_matches** passing in the **MATCH_SCORE_THRESHOLD** of what is to be considered a match, this will create a seperate data frame of just records that are set as matches.  **MATCH_SCORE_THRESHOLD** is a list so you specify different thresholds to use for each record linkage being performed. \n", "* Call **process_duplicates**  on the matched dataset and to process any duplicates between the data sources based on the key values. If there is a key from one data source that is matched with two or more different keys from the other data sources the records with the highest score is kept and the others are removed from the dataset. The final output is saved in a key called **rl_output**."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# setup a list of thresholds for each record linkage\n", "MATCH_SCORE_THRESHOLD = [.60]\n", "for thresh,key in zip(MATCH_SCORE_THRESHOLD,rl_dict):\n", "    print(f'\\nRecord Linkage: {key}, Threshold: {thresh}')\n", "    rl_dict[key]['rl'].set_matches(f\"match_score >= {thresh} and match_pred == 1\")\n", "    rl_dict[key]['rl_output'] = rl_dict[key]['rl'].process_duplicates()\n", "    rl_dict[key]['rl_output'] = rl_dict[key]['rl_output'].rename({'match_score':f'{key}_match_score'}).to_pandas()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Combine All Record Linkage Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get Matches from All Record Linkages in One Data Frame\n", "* For all the record linkages that were defined in the **rl_dict** variable, get the matched defined in the **rl_output** key and bring them together in one data frame.\n", "* Store data frame in a variable called **rl_output_all** this will have all the data partner fields and match scores from all the record linkages."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl_output_list = [rl_dict[key]['rl_output'] for key in rl_dict]\n", "rl_output_all = None\n", "for i,df in enumerate(rl_output_list):\n", "    if i == 0:\n", "        rl_output_all = df\n", "    else:\n", "        rl_output_all = rl_output_all.merge(df,how='outer')\n", "rl_output_all"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get All Records Including Non Matches\n", "* Combine the **rl_output_all** with all the data source data frames used in the record linkage process. Using outer joins this will get records that are non matches and matches in one data frame.\n", "* This data frame is used to assign an ISC_ID to ensure that all data source records that were used in the record linkage process have an ISC_ID.\n", "* In some cases the final output of a Data License Request (DLR) is interested in getting all records that match the DLR criteria and not just those that matched with another dataset.\n", "* This dataset could also be used to generate comparison groups of people not matching DLR criteria.\n", "* A field called **index** is create to generate a unique id per record. The index may be updated in the **Deduplication** section if there are multiple ids that represent the same person."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_recs_list = [rl_output_all] + [ds[d]['datasource'][[ds[d]['dataset_key']]].reset_index(drop=True) for d in ds.keys()]\n", "all_recs = None\n", "for i,df in enumerate(all_recs_list):\n", "    if i == 0:\n", "        all_recs = df\n", "    else:\n", "        all_recs = all_recs.merge(df,how='outer')\n", "all_recs = pl.from_pandas(all_recs).with_row_index('index',1)\n", "all_recs = all_recs.sort(by='index')\n", "all_recs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deduplication\n", "* There may be cases were there is a key from one data source is matched two or more keys from another data source that have the same match score. \n", "* Cases such as this will be treated as the same person by ensuring that the keys representing the same person have the same index value. \n", "* The index will then be used to assign the keys the same ISC_ID in order to deduplicate the data set and not assign mulitple ISC_IDs to the same person.\n", "* Export this as a parquet file that can be used to reference the results at a later time without rerunning the record linkage code.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    print(f'\\nRecord Linkage: {key}')\n", "    ds1_key = rl_dict[key]['rl_ds1_dataset_key']\n", "    ds2_key = rl_dict[key]['rl_ds2_dataset_key']\n", "    match_score_fld = f\"{key}_match_score\"\n", "    \n", "    all_recs = RecordLinkage.assign_duplicates_same_index2(all_recs,ds1_key)\n", "    all_recs = RecordLinkage.assign_duplicates_same_index2(all_recs,ds2_key)\n", "    all_recs = (all_recs\n", "                .filter(pl.any_horizontal(cs.exclude('index').is_not_null()))\n", "                .sort(by='index'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.makedirs('rl',exist_ok=True)\n", "all_recs_parquet_path = 'rl/all_recs.parquet'\n", "all_recs.write_parquet(all_recs_parquet_path)\n", "print(f'Data frame written to {all_recs_parquet_path}')\n", "\n", "all_recs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate ISC_IDS\n", "* **ISC_IDs** are the ids generated in place of the PII using the **index** column of the all_recs data frame.  \n", "* Call the ```RecordLinkage.generate_isc_ids(df, id_field)``` passing in the data frame and the unique id field, which results in a data frame that includes the ISC_ID field.\n", "* The ISC_ID data frame is then joined to the all_recs data frame sing the index.  After the join the index field is dropped.\n", "* A count of rows and unique keys pre and post ISC_ID generated is use to compare that no information is lost during the ISC_ID generation process.\n", "* Export this as a parquet file that can be used to reference the results at a later time without rerunning the record linkage code.\n", " "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["isc_ids_df = RecordLinkage.generate_isc_ids(all_recs,'index')\n", "print(f\"Number of ISC_ID Records: {isc_ids_df.height:,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"All Recs Dataframe Rows: {all_recs.height:,}\")\n", "print(f\"All Recs Dataframe Unique Keys: {all_recs['index'].n_unique():,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_recs_isc_id = (all_recs\n", "                   .join(isc_ids_df,on='index')\n", "                   .drop('index')\n", "                   .unique())\n", "print(f\"All Recs ISC_ID Dataframe Rows: {all_recs_isc_id.height:,}\")\n", "print(f\"All Recs ISC_ID Dataframe Unique Keys: {all_recs_isc_id['isc_id'].n_unique():,}\")\n", "\n", "os.makedirs('rl',exist_ok=True)\n", "all_recs_isc_id_parquet_path = 'rl/all_recs_isc_id.parquet'\n", "all_recs_isc_id.write_parquet(all_recs_isc_id_parquet_path)\n", "print(f'Data frame written to {all_recs_isc_id_parquet_path}')\n", "\n", "all_recs_isc_id"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Matches\n", "* Generate a data frame of only people that match with at least one other dataset.  \n", "* Export this as a parquet file that can be used to reference the record linkage results at a later time without rerunning the record linkage code.\n", "* The file can also be used to generate counts of how many people matched with at least one other dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["match_score_fields = [f'{k}_match_score' for k in rl_dict]\n", "matches_isc_id = (all_recs_isc_id\n", "                  .filter(~pl.all_horizontal([all_recs_isc_id[col].is_null() \n", "                                              for col in match_score_fields]))\n", "                  .unique()\n", "                  )\n", "\n", "os.makedirs('rl',exist_ok=True)\n", "matches_parquet_path = 'rl/matches_isc_id.parquet'\n", "matches_isc_id.write_parquet(matches_parquet_path)\n", "print(f'Data frame written to {matches_parquet_path}')\n", "\n", "matches_isc_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get the counts by score for matches\n", "for col in matches_isc_id.columns:\n", "    if col.endswith('match_score'):\n", "        print(matches_isc_id[col].value_counts().sort(by=col))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Matches Stats\n", "This section is specific to the sample dataset created to test record linkage process.  There is a sample modified code field that was created in the sample dataset that shows how the record was modfied in order to test record linkage.  One modification to the sample dataset was adding extra records that are not part of the main dataset and should not be matched.  The output will show the number of ids in the sample not including extra records, the number of ids including extra records and the number of ids matched and not matched.  For the ids not matched what sample modified code were linked to non matches."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_extra_samples = pl.from_pandas(ds2.query(\"ds2_sample_modified_code != 'Extra sample not dervived from main dataset'\"))\n", "non_extra_samples_id_counts = non_extra_samples['ds2_index'].n_unique()\n", "all_sample_id_counts = ds2['ds2_index'].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["matches_by_score_counts = matches_isc_id['ds1_ds2_match_score'].value_counts().sort(by='count',descending=True).to_pandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["matches_id_counts = matches_isc_id['ds2_index'].n_unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["extra_sample_match_id_counts = (matches_isc_id[['ds2_index']]\n", "                                .join(pl.from_pandas(ds2),\n", "                                      on='ds2_index')\n", "                                .filter(pl.col('ds2_sample_modified_code') \\\n", "                                    == 'Extra sample not dervived from main dataset')\n", "                                ['ds2_index'].n_unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_extra_samples_not_matched = non_extra_samples.join(matches_isc_id,on='ds2_index',how='anti')\n", "non_extra_samples_not_matched_id_counts = non_extra_samples_not_matched['ds2_index'].n_unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_extra_samples_not_matched_modified_code_counts = \\\n", "    (non_extra_samples_not_matched['ds2_sample_modified_code']\n", "     .value_counts()\n", "     .sort(by=['count'],descending=True).to_pandas())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Number of Ids from Sample that are In Main Dataset: \\\n", "{non_extra_samples_id_counts:,}')\n", "\n", "print(f'Number of All Ids from Sample: {all_sample_id_counts:,}')\n", "match_perc = round((matches_id_counts / non_extra_samples_id_counts),3) * 100\n", "\n", "print(f'Number of Main Ids Matched with Sample Dataset: \\\n", "{matches_id_counts:,} ({ match_perc :0.3}%)')\n", "\n", "print(f'Number of Extra Sample Ids Matched between Datasets: \\\n", "{extra_sample_match_id_counts:,}')\n", "\n", "main_ids_not_matched = non_extra_samples_id_counts - matches_id_counts\n", "main_ids_not_matched_perc = \\\n", "    round((main_ids_not_matched / non_extra_samples_id_counts),3) * 100\n", "print(f\"Number of Main Ids Not Matched With Sample Dataset: \\\n", "{main_ids_not_matched:,} ({main_ids_not_matched_perc:0.3}%)\")\n", "\n", "print(f\"\"\"\n", "Records in Main Not Matched with <PERSON><PERSON> by Modified Code:\n", "{non_extra_samples_not_matched_modified_code_counts}      \n", "\"\"\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Sample of Matches for QA\n", "**Optional:** \n", "* Check a random sample of matches to make sure the names match and check for any incorrect matches.\n", "* Enter which review fields you want included in the sample such as the **first_name_clean** and **last_name_clean** fields.\n", "* Set the **sample_frac** variable to determine the fraction of data you want to review.\n", "* Set **EXPORT_REVIEW_DATA** to True if you want to export the sample to a file.  It will create file in the **review** folder and create a CSV file named **review_data** and will be appended with a timestamp."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["review_flds = ['first_name_clean','last_name_clean']\n", "sample_frac = .01\n", "EXPORT_REVIEW_DATA = True\n", "review_list = [{\"df\":matches_isc_id,\"key\":None}] + \\\n", "    [{\"df\":RecordLinkage.get_review_data(ds[d]['datasource'],\n", "                     prefix=d,\n", "                     index_fld=ds[d]['dataset_key'],\n", "                     review_flds=review_flds\n", "                     ),\n", "      \"key\":ds[d]['dataset_key']} for d in ds.keys()]\n", "review_df = None\n", "for i,d in enumerate(review_list):\n", "    if i == 0:\n", "        review_df = d['df']\n", "    else:\n", "        review_df = review_df.join(d['df'],how='left',on=d['key'])\n", "\n", "review_df = review_df.sample(fraction=sample_frac).unique().sort(by='isc_id')\n", "if EXPORT_REVIEW_DATA:\n", "    os.makedirs('review',exist_ok=True)\n", "    _timestamp = datetime.strftime(datetime.now(),format='%Y%m%d_%H%M%S')\n", "    review_df.write_csv(f'review/review_data_{_timestamp}.csv')\n", "review_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "reclink", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}